"""
Main application orchestrator for LinkInsta automation
"""
import asyncio
import time
from pathlib import Path
from typing import List, Dict, Any
import signal
import sys

from src.config import Config
from src.utils.logger import logger
from src.message_processor import MessageProcessor
from src.video_generators import TextVideoGenerator, TextImageVideoGenerator, VideoTextGenerator
from src.audio_manager import AudioManager
from src.instagram_client import InstagramClient
from src.scheduler import PostScheduler

class LinkInstaApp:
    """Main application class that orchestrates all components"""
    
    def __init__(self):
        self.message_processor = MessageProcessor()
        self.text_video_generator = TextVideoGenerator()
        self.text_image_generator = TextImageVideoGenerator()
        self.video_text_generator = VideoTextGenerator()
        self.audio_manager = AudioManager()
        self.instagram_client = InstagramClient()
        self.scheduler = PostScheduler()
        
        self.is_running = False
        self.poll_interval = Config.POLL_INTERVAL_MINUTES * 60  # Convert to seconds
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    async def initialize(self) -> bool:
        """
        Initialize the application
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Initializing LinkInsta automation app...")
            
            # Validate configuration
            Config.validate_config()
            
            # Create necessary directories
            Config.create_directories()
            
            # Download background music if audio directory is empty
            audio_files = list(Config.AUDIO_DIR.glob("*.mp3")) + list(Config.AUDIO_DIR.glob("*.wav"))
            if len(audio_files) < 5:
                logger.info("Downloading background music...")
                self.audio_manager.download_copyright_free_music(20)
            
            # Test Instagram login (skip only in demo mode)
            if not Config.DEMO_MODE:
                if not self.instagram_client.login():
                    logger.error("Failed to login to Instagram")
                    return False

                if not Config.ENABLE_INSTAGRAM_POSTING:
                    logger.info("Instagram posting disabled (but logged in for testing)")
            else:
                logger.info("Demo mode: Skipping Instagram login completely")
            
            logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing application: {e}")
            return False
    
    async def process_new_messages(self) -> int:
        """
        Process new messages from Telegram
        
        Returns:
            Number of messages processed
        """
        try:
            logger.info("Processing new messages...")
            
            # Poll for new messages
            new_messages = await self.message_processor.poll_and_process_messages()
            
            if not new_messages:
                logger.info("No new messages to process")
                return 0
            
            processed_count = 0
            
            for message in new_messages:
                try:
                    if message.should_skip:
                        logger.info(f"Skipping message {message.telegram_message.message_id}: V2ray config")
                        continue
                    
                    # Generate video based on content type
                    success = await self._generate_video_for_message(message)
                    
                    if success:
                        processed_count += 1
                        logger.info(f"Successfully processed message {message.telegram_message.message_id}")
                    else:
                        logger.error(f"Failed to process message {message.telegram_message.message_id}")
                
                except Exception as e:
                    logger.error(f"Error processing message {message.telegram_message.message_id}: {e}")
            
            logger.info(f"Processed {processed_count} out of {len(new_messages)} messages")
            return processed_count
            
        except Exception as e:
            logger.error(f"Error processing new messages: {e}")
            return 0
    
    async def _generate_video_for_message(self, message) -> bool:
        """Generate video for a specific message"""
        try:
            content_type = message.content_type
            text_content = message.text_content
            
            # Generate unique output filename
            timestamp = int(time.time())
            output_filename = f"{content_type}_{message.telegram_message.message_id}_{timestamp}.mp4"
            output_path = Config.OUTPUT_DIR / output_filename
            
            success = False
            
            if content_type == 'text_only':
                success = self.text_video_generator.generate_video(text_content, output_path)
            
            elif content_type == 'text_image':
                success = self.text_image_generator.generate_video(
                    text_content, message.media_paths, output_path
                )
            
            elif content_type == 'video_text':
                if message.media_paths:
                    video_path = message.media_paths[0]  # Use first video
                    success = self.video_text_generator.generate_video(
                        text_content, video_path, output_path
                    )
                else:
                    logger.error("No video file found for video_text message")
                    return False
            
            if success:
                # Mark video as generated
                self.message_processor.mark_video_generated(message, output_path)

                # Send video to admin chat for review
                await self._send_video_to_admin_chat(output_path, message)

                return True
            else:
                logger.error(f"Failed to generate video for message {message.telegram_message.message_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error generating video for message: {e}")
            return False

    async def _send_video_to_admin_chat(self, video_path: Path, message) -> bool:
        """Send generated video to admin chat for review"""
        try:
            from src.telegram_client import TelegramClient

            # Create caption with message info
            caption = f"🎥 Generated Video\n\n"
            caption += f"📝 Type: {message.content_type}\n"
            caption += f"🆔 Message ID: {message.telegram_message.message_id}\n"
            caption += f"📄 Text: {message.text_content[:100]}{'...' if len(message.text_content) > 100 else ''}\n"
            caption += f"📁 File: {video_path.name}\n\n"
            caption += f"✅ Ready for Instagram posting"

            # Send to personal chat
            telegram_client = TelegramClient()
            success = await telegram_client.send_video(
                video_path=video_path,
                caption=caption,
                chat_id=Config.TELEGRAM_PERSONAL_CHAT_ID
            )

            if success:
                logger.info(f"✅ Sent video to admin chat: {video_path.name}")
            else:
                logger.error(f"❌ Failed to send video to admin chat: {video_path.name}")

            return success

        except Exception as e:
            logger.error(f"Error sending video to admin chat: {e}")
            return False
    
    def schedule_ready_posts(self) -> int:
        """Schedule posts that are ready for Instagram"""
        try:
            return self.scheduler.schedule_ready_posts()
        except Exception as e:
            logger.error(f"Error scheduling posts: {e}")
            return 0

    def send_schedule_summary(self):
        """Send Instagram schedule summary to admin"""
        try:
            self.scheduler.send_schedule_summary()
        except Exception as e:
            logger.error(f"Error sending schedule summary: {e}")
    
    async def run_cycle(self):
        """Run one complete processing cycle"""
        try:
            logger.info("Starting processing cycle...")
            
            # Process new messages
            processed_messages = await self.process_new_messages()
            
            # Schedule ready posts
            scheduled_posts = self.schedule_ready_posts()
            
            # Clean up old temporary files
            self.message_processor.cleanup_old_temp_files()
            
            # Clean up old schedule entries
            self.scheduler.cleanup_old_entries()
            
            logger.info(f"Cycle complete: {processed_messages} messages processed, {scheduled_posts} posts scheduled")
            
        except Exception as e:
            logger.error(f"Error in processing cycle: {e}")
    
    async def run(self):
        """Run the main application loop"""
        try:
            if not await self.initialize():
                logger.error("Failed to initialize application")
                return
            
            # Start the post scheduler
            self.scheduler.start_scheduler()

            # Send initial schedule summary to admin
            self.send_schedule_summary()

            self.is_running = True
            logger.info(f"Starting main loop with {self.poll_interval/60:.1f} minute intervals...")
            
            while self.is_running:
                try:
                    await self.run_cycle()
                    
                    # Wait for next cycle
                    logger.info(f"Waiting {self.poll_interval/60:.1f} minutes until next cycle...")
                    await asyncio.sleep(self.poll_interval)
                    
                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt, shutting down...")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    # Wait a bit before retrying
                    await asyncio.sleep(60)
            
        except Exception as e:
            logger.error(f"Fatal error in main application: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the application"""
        logger.info("Stopping LinkInsta application...")
        
        self.is_running = False
        
        # Stop scheduler
        self.scheduler.stop_scheduler()
        
        # Logout from Instagram
        self.instagram_client.logout()
        
        logger.info("Application stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current application status"""
        try:
            # Get statistics from various components
            message_stats = self.message_processor.get_statistics()
            audio_stats = self.audio_manager.get_usage_statistics()
            instagram_stats = self.instagram_client.get_post_statistics()
            schedule_stats = self.scheduler.get_schedule_status()
            
            return {
                'is_running': self.is_running,
                'poll_interval_minutes': self.poll_interval / 60,
                'message_processing': message_stats,
                'audio_management': audio_stats,
                'instagram_posting': instagram_stats,
                'scheduling': schedule_stats,
                'last_status_check': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting application status: {e}")
            return {'error': str(e)}

# Main entry point
async def main():
    """Main entry point for the application"""
    app = LinkInstaApp()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
