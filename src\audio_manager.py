"""
Audio management system for downloading and managing background music
"""
import os
import json
import random
import requests
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime
import time

from src.config import Config
from src.utils.logger import logger
from src.trending_music_manager import TrendingMusicManager

class AudioManager:
    """Manages background music for video generation"""
    
    def __init__(self):
        self.audio_dir = Config.AUDIO_DIR
        self.usage_tracking_file = self.audio_dir / "usage_tracking.json"
        self.usage_data = self._load_usage_data()

        # Initialize trending music manager
        self.trending_manager = TrendingMusicManager()
        
        # Copyright-free lo-fi music sources
        self.music_sources = [
            {
                "name": "Lofi Study Session 1",
                "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Placeholder
                "duration": 180,
                "genre": "lofi"
            },
            # Note: In a real implementation, you would need actual copyright-free music URLs
            # For now, we'll create a system that can download from various sources
        ]
    
    def _load_usage_data(self) -> Dict:
        """Load audio usage tracking data"""
        if self.usage_tracking_file.exists():
            try:
                with open(self.usage_tracking_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load usage data: {e}")
        
        return {
            "tracks": {},
            "last_used": {},
            "download_history": []
        }
    
    def _save_usage_data(self):
        """Save audio usage tracking data"""
        try:
            with open(self.usage_tracking_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save usage data: {e}")
    
    def download_copyright_free_music(self, count: int = 20) -> bool:
        """
        Download copyright-free lo-fi music tracks
        
        Args:
            count: Number of tracks to download
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Starting download of {count} copyright-free music tracks...")
        
        # Create audio directory if it doesn't exist
        self.audio_dir.mkdir(exist_ok=True)
        
        # List of copyright-free lo-fi music URLs (you would need to populate this with real URLs)
        # For demonstration, I'll create a list of placeholder tracks
        music_urls = self._get_copyright_free_music_urls()
        
        downloaded_count = 0
        
        for i, music_info in enumerate(music_urls[:count]):
            try:
                filename = f"lofi_track_{i+1:02d}.mp3"
                output_path = self.audio_dir / filename
                
                if output_path.exists():
                    logger.info(f"Track already exists: {filename}")
                    downloaded_count += 1
                    continue
                
                # Download the track
                if self._download_track(music_info["url"], output_path):
                    # Update tracking data
                    self.usage_data["tracks"][filename] = {
                        "source": music_info.get("source", "unknown"),
                        "genre": music_info.get("genre", "lofi"),
                        "duration": music_info.get("duration", 0),
                        "download_date": datetime.now().isoformat(),
                        "usage_count": 0
                    }
                    
                    self.usage_data["download_history"].append({
                        "filename": filename,
                        "download_date": datetime.now().isoformat(),
                        "source": music_info.get("source", "unknown")
                    })
                    
                    downloaded_count += 1
                    logger.info(f"Downloaded: {filename}")
                    
                    # Add delay to be respectful to servers
                    time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error downloading track {i+1}: {e}")
        
        # Save updated tracking data
        self._save_usage_data()
        
        logger.info(f"Downloaded {downloaded_count} out of {count} requested tracks")
        return downloaded_count > 0
    
    def _get_copyright_free_music_urls(self) -> List[Dict]:
        """
        Get list of copyright-free music URLs
        
        Note: In a real implementation, you would need to:
        1. Use APIs from services like Freesound.org, Zapsplat, etc.
        2. Use YouTube Audio Library API
        3. Use other copyright-free music sources
        
        For now, this returns placeholder data
        """
        # Placeholder data - in real implementation, replace with actual sources
        placeholder_tracks = []
        
        # Generate placeholder tracks for demonstration
        for i in range(20):
            placeholder_tracks.append({
                "url": f"https://example.com/lofi_track_{i+1}.mp3",  # Placeholder URL
                "source": "placeholder",
                "genre": "lofi",
                "duration": random.randint(120, 300),  # 2-5 minutes
                "title": f"Lo-Fi Study Track {i+1}"
            })
        
        return placeholder_tracks
    
    def _download_track(self, url: str, output_path: Path) -> bool:
        """
        Download a single music track
        
        Args:
            url: URL of the music track
            output_path: Path where the track should be saved
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # For placeholder URLs, create a dummy file
            if "example.com" in url:
                # Create a small dummy audio file for testing
                output_path.write_text("# Placeholder audio file\n# In real implementation, this would be actual audio data")
                return True
            
            # Real download implementation
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return True
            
        except Exception as e:
            logger.error(f"Error downloading from {url}: {e}")
            return False
    
    def get_random_track(self, avoid_recent: bool = True, genre_preference: Optional[str] = None) -> Optional[Path]:
        """
        Get a trending background music track

        Args:
            avoid_recent: Whether to avoid recently used tracks
            genre_preference: Preferred music genre (e.g., 'lo-fi', 'upbeat')

        Returns:
            Path to a music file, or None if no tracks available
        """
        try:
            # First try to get trending music
            trending_track = self.trending_manager.get_trending_track(
                genre_preference=genre_preference,
                avoid_recent=avoid_recent
            )

            if trending_track and trending_track.exists():
                logger.info(f"Using trending track: {trending_track.name}")
                return trending_track

            # Fallback to existing audio files
            logger.info("Falling back to existing audio files")
            audio_files = list(self.audio_dir.glob("*.mp3")) + list(self.audio_dir.glob("*.wav"))

            if not audio_files:
                logger.warning("No audio files found in audio directory")
                return None

            if not avoid_recent or len(audio_files) <= 3:
                # If we have few tracks or not avoiding recent, just pick randomly
                selected_track = random.choice(audio_files)
                self._track_usage(selected_track.name)
                return selected_track

            # Filter out recently used tracks
            now = datetime.now()
            recent_tracks = set()

            for filename, last_used_str in self.usage_data.get("last_used", {}).items():
                try:
                    last_used = datetime.fromisoformat(last_used_str)
                    hours_since_use = (now - last_used).total_seconds() / 3600

                    # Consider tracks used in last 24 hours as "recent"
                    if hours_since_use < 24:
                        recent_tracks.add(filename)
                except:
                    pass

            # Filter available tracks
            available_tracks = [
                track for track in audio_files
                if track.name not in recent_tracks
            ]

            if not available_tracks:
                # If all tracks are recent, use the least recently used
                available_tracks = audio_files

            selected_track = random.choice(available_tracks)

            # Update usage tracking
            self._track_usage(selected_track.name)

            return selected_track

        except Exception as e:
            logger.error(f"Error getting random track: {e}")
            return None
    
    def _track_usage(self, filename: str):
        """Track usage of a music file"""
        try:
            # Update last used time
            self.usage_data["last_used"][filename] = datetime.now().isoformat()
            
            # Update usage count
            if filename not in self.usage_data["tracks"]:
                self.usage_data["tracks"][filename] = {
                    "usage_count": 0,
                    "first_used": datetime.now().isoformat()
                }
            
            self.usage_data["tracks"][filename]["usage_count"] += 1
            
            # Save updated data
            self._save_usage_data()
            
        except Exception as e:
            logger.error(f"Error tracking usage for {filename}: {e}")

    def get_trending_music_stats(self) -> Dict:
        """Get statistics about trending music usage"""
        try:
            return self.trending_manager.get_usage_stats()
        except Exception as e:
            logger.error(f"Error getting trending music stats: {e}")
            return {}

    def update_trending_music(self, force_update: bool = False) -> bool:
        """Update trending music cache"""
        try:
            return self.trending_manager.update_trending_music(force_update)
        except Exception as e:
            logger.error(f"Error updating trending music: {e}")
            return False

    def get_trending_genres(self) -> List[str]:
        """Get list of trending music genres"""
        try:
            return self.trending_manager.get_trending_genres()
        except Exception as e:
            logger.error(f"Error getting trending genres: {e}")
            return ["lo-fi", "chill", "upbeat"]  # Fallback genres
    
    def get_usage_statistics(self) -> Dict:
        """Get usage statistics for audio tracks"""
        try:
            total_tracks = len(list(self.audio_dir.glob("*.mp3")) + list(self.audio_dir.glob("*.wav")))
            total_usage = sum(track.get("usage_count", 0) for track in self.usage_data.get("tracks", {}).values())
            
            most_used = None
            max_usage = 0
            
            for filename, data in self.usage_data.get("tracks", {}).items():
                usage_count = data.get("usage_count", 0)
                if usage_count > max_usage:
                    max_usage = usage_count
                    most_used = filename
            
            return {
                "total_tracks": total_tracks,
                "total_usage": total_usage,
                "most_used_track": most_used,
                "most_used_count": max_usage,
                "tracks_with_usage": len(self.usage_data.get("tracks", {}))
            }
            
        except Exception as e:
            logger.error(f"Error getting usage statistics: {e}")
            return {}
    
    def cleanup_unused_tracks(self, min_usage_threshold: int = 0) -> int:
        """
        Clean up tracks that haven't been used much
        
        Args:
            min_usage_threshold: Minimum usage count to keep a track
            
        Returns:
            Number of tracks removed
        """
        try:
            removed_count = 0
            
            for filename, data in list(self.usage_data.get("tracks", {}).items()):
                usage_count = data.get("usage_count", 0)
                
                if usage_count <= min_usage_threshold:
                    track_path = self.audio_dir / filename
                    if track_path.exists():
                        track_path.unlink()
                        logger.info(f"Removed unused track: {filename}")
                        removed_count += 1
                    
                    # Remove from tracking data
                    del self.usage_data["tracks"][filename]
                    if filename in self.usage_data.get("last_used", {}):
                        del self.usage_data["last_used"][filename]
            
            # Save updated data
            self._save_usage_data()
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up unused tracks: {e}")
            return 0
