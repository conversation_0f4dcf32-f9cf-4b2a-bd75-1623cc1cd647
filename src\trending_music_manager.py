"""
Trending Music Manager for Instagram Videos
Finds and manages trending, copyright-free music for video generation
"""

import json
import logging
import random
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
from urllib.parse import quote

from .config import Config

logger = logging.getLogger(__name__)


class TrendingMusicManager:
    """Manages trending, copyright-free music for Instagram videos"""
    
    def __init__(self):
        self.audio_dir = Config.AUDIO_DIR
        self.trending_cache_file = self.audio_dir / "trending_music_cache.json"
        self.usage_tracking_file = self.audio_dir / "trending_usage_tracking.json"
        self.trending_cache = self._load_trending_cache()
        self.usage_data = self._load_usage_data()
        
        # Copyright-free music sources
        self.music_sources = {
            "freesound": {
                "api_key": None,  # User should set this in config
                "base_url": "https://freesound.org/apiv2",
                "search_endpoint": "/search/text/",
                "download_endpoint": "/sounds/{id}/download/"
            },
            "youtube_audio_library": {
                # YouTube Audio Library doesn't have a public API
                # We'll use curated lists of known copyright-free tracks
                "curated_tracks": []
            },
            "pixabay": {
                "api_key": None,  # User should set this in config
                "base_url": "https://pixabay.com/api/music/",
                "search_params": {
                    "category": "music",
                    "min_duration": 30,
                    "max_duration": 300
                }
            }
        }
        
        # Trending music genres and styles popular on Instagram
        self.trending_genres = [
            "lo-fi", "chill", "ambient", "electronic", "pop", "indie",
            "acoustic", "upbeat", "motivational", "relaxing", "energetic"
        ]
        
        # Fallback copyright-free tracks (curated list)
        self.fallback_tracks = [
            {
                "title": "Chill Lo-Fi Beat",
                "genre": "lo-fi",
                "duration": 180,
                "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Placeholder
                "source": "curated"
            },
            {
                "title": "Upbeat Electronic",
                "genre": "electronic", 
                "duration": 200,
                "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Placeholder
                "source": "curated"
            }
        ]
    
    def _load_trending_cache(self) -> Dict:
        """Load trending music cache from file"""
        try:
            if self.trending_cache_file.exists():
                with open(self.trending_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load trending cache: {e}")
        
        return {
            "last_updated": None,
            "trending_tracks": [],
            "genre_trends": {}
        }
    
    def _save_trending_cache(self):
        """Save trending music cache to file"""
        try:
            self.audio_dir.mkdir(exist_ok=True)
            with open(self.trending_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.trending_cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save trending cache: {e}")
    
    def _load_usage_data(self) -> Dict:
        """Load usage tracking data"""
        try:
            if self.usage_tracking_file.exists():
                with open(self.usage_tracking_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load usage data: {e}")
        
        return {
            "tracks": {},
            "last_used": {},
            "download_history": [],
            "trending_history": []
        }
    
    def _save_usage_data(self):
        """Save usage tracking data"""
        try:
            self.audio_dir.mkdir(exist_ok=True)
            with open(self.usage_tracking_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save usage data: {e}")
    
    def update_trending_music(self, force_update: bool = False) -> bool:
        """
        Update trending music cache from various sources
        
        Args:
            force_update: Force update even if cache is recent
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if we need to update
            if not force_update and self._is_cache_recent():
                logger.info("Trending music cache is recent, skipping update")
                return True
            
            logger.info("Updating trending music cache...")
            
            trending_tracks = []
            
            # Try to get trending music from various sources
            trending_tracks.extend(self._get_freesound_trending())
            trending_tracks.extend(self._get_pixabay_trending())
            trending_tracks.extend(self._get_curated_trending())
            
            if trending_tracks:
                self.trending_cache["trending_tracks"] = trending_tracks
                self.trending_cache["last_updated"] = datetime.now().isoformat()
                self._save_trending_cache()
                
                logger.info(f"Updated trending music cache with {len(trending_tracks)} tracks")
                return True
            else:
                logger.warning("No trending tracks found, using fallback")
                return False
                
        except Exception as e:
            logger.error(f"Error updating trending music: {e}")
            return False
    
    def _is_cache_recent(self) -> bool:
        """Check if cache was updated recently (within 24 hours)"""
        if not self.trending_cache.get("last_updated"):
            return False
        
        try:
            last_updated = datetime.fromisoformat(self.trending_cache["last_updated"])
            return (datetime.now() - last_updated) < timedelta(hours=24)
        except:
            return False
    
    def _get_freesound_trending(self) -> List[Dict]:
        """Get trending music from Freesound.org"""
        tracks = []
        
        try:
            # Note: This requires a Freesound API key
            api_key = self.music_sources["freesound"]["api_key"]
            if not api_key:
                logger.info("Freesound API key not configured, skipping")
                return tracks
            
            base_url = self.music_sources["freesound"]["base_url"]
            
            # Search for trending genres
            for genre in self.trending_genres[:3]:  # Limit to avoid rate limits
                search_url = f"{base_url}/search/text/"
                params = {
                    "query": f"{genre} music",
                    "filter": "duration:[30.0 TO 300.0] AND type:wav",
                    "sort": "downloads_desc",  # Sort by popularity
                    "page_size": 5,
                    "token": api_key
                }
                
                response = requests.get(search_url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    for result in data.get("results", []):
                        tracks.append({
                            "title": result.get("name", "Unknown"),
                            "genre": genre,
                            "duration": int(result.get("duration", 180)),
                            "url": result.get("previews", {}).get("preview-hq-mp3", ""),
                            "source": "freesound",
                            "id": result.get("id"),
                            "downloads": result.get("num_downloads", 0)
                        })
                
                time.sleep(0.5)  # Rate limiting
                
        except Exception as e:
            logger.warning(f"Error getting Freesound trending: {e}")
        
        return tracks
    
    def _get_pixabay_trending(self) -> List[Dict]:
        """Get trending music from Pixabay"""
        tracks = []
        
        try:
            api_key = self.music_sources["pixabay"]["api_key"]
            if not api_key:
                logger.info("Pixabay API key not configured, skipping")
                return tracks
            
            base_url = self.music_sources["pixabay"]["base_url"]
            
            for genre in self.trending_genres[:3]:
                params = {
                    "key": api_key,
                    "q": genre,
                    "category": "music",
                    "min_duration": 30,
                    "max_duration": 300,
                    "order": "popular",
                    "per_page": 5
                }
                
                response = requests.get(base_url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    for result in data.get("hits", []):
                        tracks.append({
                            "title": result.get("tags", genre).replace(",", " "),
                            "genre": genre,
                            "duration": result.get("duration", 180),
                            "url": result.get("webformatURL", ""),
                            "source": "pixabay",
                            "id": result.get("id"),
                            "downloads": result.get("downloads", 0)
                        })
                
                time.sleep(0.5)  # Rate limiting
                
        except Exception as e:
            logger.warning(f"Error getting Pixabay trending: {e}")
        
        return tracks
    
    def _get_curated_trending(self) -> List[Dict]:
        """Get curated trending music (fallback)"""
        # Return curated list with trending score based on genre popularity
        trending_tracks = []
        
        for track in self.fallback_tracks:
            track_copy = track.copy()
            track_copy["trending_score"] = random.randint(50, 100)
            track_copy["last_trending"] = datetime.now().isoformat()
            trending_tracks.append(track_copy)
        
        return trending_tracks

    def get_trending_track(self, genre_preference: Optional[str] = None, avoid_recent: bool = True) -> Optional[Path]:
        """
        Get a trending music track for video generation

        Args:
            genre_preference: Preferred genre (e.g., 'lo-fi', 'upbeat')
            avoid_recent: Whether to avoid recently used tracks

        Returns:
            Path to downloaded music file, or None if no tracks available
        """
        try:
            # Update trending cache if needed
            self.update_trending_music()

            # Get available trending tracks
            trending_tracks = self.trending_cache.get("trending_tracks", [])

            if not trending_tracks:
                logger.warning("No trending tracks available, using fallback")
                return self._get_fallback_track()

            # Filter by genre preference if specified
            if genre_preference:
                filtered_tracks = [t for t in trending_tracks if t.get("genre") == genre_preference]
                if filtered_tracks:
                    trending_tracks = filtered_tracks

            # Filter out recently used tracks if requested
            if avoid_recent:
                trending_tracks = self._filter_recent_tracks(trending_tracks)

            if not trending_tracks:
                logger.info("All trending tracks recently used, using any available")
                trending_tracks = self.trending_cache.get("trending_tracks", [])

            # Select track based on trending score
            selected_track = self._select_trending_track(trending_tracks)

            if selected_track:
                # Download track if not already cached
                track_path = self._download_trending_track(selected_track)
                if track_path:
                    self._track_usage(track_path.name, selected_track)
                    return track_path

            # Fallback to existing audio files
            return self._get_fallback_track()

        except Exception as e:
            logger.error(f"Error getting trending track: {e}")
            return self._get_fallback_track()

    def _filter_recent_tracks(self, tracks: List[Dict]) -> List[Dict]:
        """Filter out recently used tracks"""
        now = datetime.now()
        recent_threshold = timedelta(hours=12)  # Consider tracks used in last 12 hours as recent

        filtered_tracks = []

        for track in tracks:
            track_id = track.get("id", track.get("title", ""))
            last_used_str = self.usage_data.get("last_used", {}).get(str(track_id))

            if not last_used_str:
                filtered_tracks.append(track)
                continue

            try:
                last_used = datetime.fromisoformat(last_used_str)
                if (now - last_used) > recent_threshold:
                    filtered_tracks.append(track)
            except:
                filtered_tracks.append(track)  # Include if we can't parse date

        return filtered_tracks

    def _select_trending_track(self, tracks: List[Dict]) -> Optional[Dict]:
        """Select a track based on trending score and other factors"""
        if not tracks:
            return None

        # Sort by trending score, downloads, and recency
        def track_score(track):
            trending_score = track.get("trending_score", 0)
            downloads = track.get("downloads", 0)
            # Normalize downloads to 0-100 scale
            download_score = min(downloads / 1000 * 100, 100) if downloads else 0
            return trending_score + download_score

        tracks.sort(key=track_score, reverse=True)

        # Use weighted random selection favoring higher scores
        weights = [track_score(track) + 1 for track in tracks]  # +1 to avoid zero weights

        import random
        selected_track = random.choices(tracks, weights=weights)[0]

        return selected_track

    def _download_trending_track(self, track: Dict) -> Optional[Path]:
        """Download a trending track to local storage"""
        try:
            track_id = track.get("id", "")
            track_title = track.get("title", "unknown")
            track_url = track.get("url", "")

            if not track_url:
                logger.warning(f"No URL for track: {track_title}")
                return None

            # Create filename
            safe_title = "".join(c for c in track_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"trending_{track_id}_{safe_title[:30]}.mp3"
            output_path = self.audio_dir / filename

            # Check if already downloaded
            if output_path.exists():
                logger.info(f"Track already cached: {filename}")
                return output_path

            # Download the track
            logger.info(f"Downloading trending track: {track_title}")

            response = requests.get(track_url, timeout=30, stream=True)
            response.raise_for_status()

            # Ensure audio directory exists
            self.audio_dir.mkdir(exist_ok=True)

            # Save the file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Update download history
            self.usage_data["download_history"].append({
                "filename": filename,
                "track_id": str(track_id),
                "title": track_title,
                "source": track.get("source", "unknown"),
                "download_date": datetime.now().isoformat()
            })

            self._save_usage_data()

            logger.info(f"Successfully downloaded: {filename}")
            return output_path

        except Exception as e:
            logger.error(f"Error downloading track {track.get('title', 'unknown')}: {e}")
            return None

    def _get_fallback_track(self) -> Optional[Path]:
        """Get a fallback track from existing audio files"""
        try:
            # Get all audio files
            audio_files = list(self.audio_dir.glob("*.mp3")) + list(self.audio_dir.glob("*.wav"))

            if not audio_files:
                logger.warning("No audio files found in audio directory")
                return None

            # Prefer non-trending files for fallback
            non_trending = [f for f in audio_files if not f.name.startswith("trending_")]
            if non_trending:
                return random.choice(non_trending)

            return random.choice(audio_files)

        except Exception as e:
            logger.error(f"Error getting fallback track: {e}")
            return None

    def _track_usage(self, filename: str, track_info: Dict):
        """Track usage of a music file"""
        try:
            track_id = str(track_info.get("id", filename))

            # Update last used time
            self.usage_data["last_used"][track_id] = datetime.now().isoformat()

            # Update usage count
            if track_id not in self.usage_data["tracks"]:
                self.usage_data["tracks"][track_id] = {
                    "filename": filename,
                    "title": track_info.get("title", "Unknown"),
                    "genre": track_info.get("genre", "unknown"),
                    "source": track_info.get("source", "unknown"),
                    "usage_count": 0,
                    "first_used": datetime.now().isoformat()
                }

            self.usage_data["tracks"][track_id]["usage_count"] += 1

            # Save updated data
            self._save_usage_data()

        except Exception as e:
            logger.error(f"Error tracking usage for {filename}: {e}")

    def get_trending_genres(self) -> List[str]:
        """Get list of currently trending music genres"""
        return self.trending_genres.copy()

    def get_usage_stats(self) -> Dict:
        """Get usage statistics for trending music"""
        return {
            "total_tracks": len(self.usage_data.get("tracks", {})),
            "total_downloads": len(self.usage_data.get("download_history", [])),
            "cache_last_updated": self.trending_cache.get("last_updated"),
            "trending_tracks_count": len(self.trending_cache.get("trending_tracks", []))
        }
