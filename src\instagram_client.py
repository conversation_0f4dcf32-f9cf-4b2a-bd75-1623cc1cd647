"""
Instagram client for posting reels using instagrapi
"""
import os
import json
import time
import random
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from instagrapi import Client
from instagrapi.exceptions import (
    <PERSON><PERSON>Required,
    ChallengeRequired,
    PleaseWaitFewMinutes,
    ClientError,
    MediaNotFound,
    UserNotFound
)

from src.config import Config
from src.utils.logger import logger

class InstagramClient:
    """Instagram client for posting reels"""
    
    def __init__(self):
        self.username = Config.INSTAGRAM_USERNAME
        self.password = Config.INSTAGRAM_PASSWORD
        self.session_file = Config.INSTAGRAM_SESSION_FILE
        self.client = Client()
        self.is_logged_in = False
        
        # Rate limiting
        self.last_post_time = None
        self.min_interval_seconds = 3600  # 1 hour minimum between posts
        
        # Post tracking
        self.post_history_file = Config.BASE_DIR / "instagram_post_history.json"
        self.post_history = self._load_post_history()
    
    def _load_post_history(self) -> Dict:
        """Load Instagram post history"""
        if self.post_history_file.exists():
            try:
                with open(self.post_history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load post history: {e}")
        
        return {
            "posts": [],
            "total_posts": 0,
            "last_post_date": None,
            "failed_posts": []
        }
    
    def _save_post_history(self):
        """Save Instagram post history"""
        try:
            with open(self.post_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.post_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save post history: {e}")
    
    def _load_session(self) -> bool:
        """Load existing Instagram session"""
        try:
            if self.session_file.exists():
                self.client.load_settings(str(self.session_file))
                logger.info("Loaded existing Instagram session")
                return True
        except Exception as e:
            logger.warning(f"Could not load session: {e}")
        return False
    
    def _save_session(self):
        """Save Instagram session"""
        try:
            self.client.dump_settings(str(self.session_file))
            logger.info("Saved Instagram session")
        except Exception as e:
            logger.error(f"Could not save session: {e}")

    def _remove_invalid_session(self):
        """Remove invalid session file to force fresh login"""
        try:
            if self.session_file.exists():
                self.session_file.unlink()
                logger.info("Removed invalid Instagram session file")
        except Exception as e:
            logger.warning(f"Could not remove session file: {e}")
    
    def login(self) -> bool:
        """
        Login to Instagram
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Try to load existing session first
            if self._load_session():
                try:
                    # Test if session is still valid
                    self.client.get_timeline_feed()
                    self.is_logged_in = True
                    logger.info("Successfully logged in with existing session")
                    return True
                except LoginRequired:
                    logger.info("Session expired, need to login again")
                    self._remove_invalid_session()
                except Exception as e:
                    error_str = str(e)
                    # Check for specific Instagram logout messages
                    if "user_has_logged_out" in error_str or "logout_reason" in error_str:
                        logger.warning("Instagram session invalidated by Instagram, removing session file")
                        self._remove_invalid_session()
                    else:
                        logger.warning(f"Session validation failed: {e}")
                        # For other errors, also remove session to force fresh login
                        self._remove_invalid_session()
            
            # Login with credentials
            logger.info(f"Logging in to Instagram as {self.username}")

            # Create a fresh client instance for clean login
            self.client = Client()

            # Set some client settings to appear more human-like
            self.client.delay_range = [1, 3]

            # Add additional settings to avoid detection (compatible with newer instagrapi)
            try:
                self.client.set_user_agent("Instagram 219.0.0.12.117 Android")
            except AttributeError:
                # Newer version might not have this method
                pass

            try:
                # Try to set device settings if method exists
                if hasattr(self.client, 'set_device_settings'):
                    self.client.set_device_settings({
                        "app_version": "219.0.0.12.117",
                        "android_version": 29,
                        "android_release": "10",
                        "dpi": "480dpi",
                        "resolution": "1080x2340",
                        "manufacturer": "samsung",
                        "device": "SM-G975F",
                        "model": "galaxy s10+",
                        "cpu": "exynos9820",
                        "version_code": "314665256"
                    })
                else:
                    # Use newer method or skip if not available
                    logger.info("Device settings method not available in this instagrapi version")
            except Exception as e:
                logger.warning(f"Could not set device settings: {e}")

            success = self.client.login(self.username, self.password)
            
            if success:
                self.is_logged_in = True
                self._save_session()
                logger.info("Successfully logged in to Instagram")
                return True
            else:
                logger.error("Failed to login to Instagram")
                return False
                
        except ChallengeRequired as e:
            logger.error(f"Instagram challenge required: {e}")
            # In a production environment, you might want to handle challenges
            return False
        except PleaseWaitFewMinutes as e:
            logger.error(f"Instagram rate limit: {e}")
            return False
        except Exception as e:
            logger.error(f"Error logging in to Instagram: {e}")
            return False
    
    def _prepare_caption(self, text: str) -> str:
        """
        Prepare caption for Instagram post
        
        Args:
            text: Original text content
            
        Returns:
            Formatted caption
        """
        # Clean up text
        caption = text.strip()
        
        # Add hashtags and branding
        hashtags = [
            "#linky",
            "#persian",
            "#farsi",
            "#content",
            "#reel",
            "#instagram",
            "#viral"
        ]
        
        # Limit caption length (Instagram has a 2200 character limit)
        max_text_length = 2000 - len(" ".join(hashtags)) - 50  # Leave room for hashtags and spacing
        
        if len(caption) > max_text_length:
            caption = caption[:max_text_length - 3] + "..."
        
        # Add branding and hashtags
        final_caption = f"{caption}\n\n🔥 Follow @{Config.CHANNEL_NAME} for more content!\n\n{' '.join(hashtags)}"
        
        return final_caption
    
    def _can_post_now(self) -> bool:
        """Check if enough time has passed since last post"""
        if not self.last_post_time:
            return True
        
        time_since_last_post = time.time() - self.last_post_time
        return time_since_last_post >= self.min_interval_seconds
    
    def post_reel(self, video_path: Path, caption: str, thumbnail_path: Path = None) -> Optional[str]:
        """
        Post a reel to Instagram
        
        Args:
            video_path: Path to the video file
            caption: Caption for the post
            thumbnail_path: Optional custom thumbnail
            
        Returns:
            Media ID if successful, None otherwise
        """
        try:
            if not self.is_logged_in:
                if not self.login():
                    logger.error("Cannot post reel: not logged in")
                    return None
            
            if not self._can_post_now():
                wait_time = self.min_interval_seconds - (time.time() - self.last_post_time)
                logger.warning(f"Rate limit: need to wait {wait_time/60:.1f} minutes before posting")
                return None
            
            if not video_path.exists():
                logger.error(f"Video file not found: {video_path}")
                return None
            
            # Prepare caption
            formatted_caption = self._prepare_caption(caption)
            
            logger.info(f"Posting reel to Instagram: {video_path.name}")
            
            # Upload reel with better error handling
            try:
                media = self.client.clip_upload(
                    path=str(video_path),
                    caption=formatted_caption,
                    thumbnail=str(thumbnail_path) if thumbnail_path and thumbnail_path.exists() else None
                )
            except (ClientError, UserNotFound, MediaNotFound) as e:
                logger.error(f"Instagram API error during upload: {e}")
                self._record_failed_post(video_path, caption, f"Instagram API error: {str(e)}")
                return None
            except Exception as e:
                # Handle any other unexpected errors (like Pydantic validation errors)
                logger.error(f"Unexpected error during Instagram upload: {e}")
                self._record_failed_post(video_path, caption, f"Unexpected error: {str(e)}")
                return None
            
            if media:
                media_id = media.id
                self.last_post_time = time.time()
                
                # Update post history
                post_record = {
                    "media_id": media_id,
                    "video_path": str(video_path),
                    "caption": formatted_caption,
                    "post_date": datetime.now().isoformat(),
                    "success": True
                }
                
                self.post_history["posts"].append(post_record)
                self.post_history["total_posts"] += 1
                self.post_history["last_post_date"] = datetime.now().isoformat()
                
                self._save_post_history()
                
                logger.info(f"Successfully posted reel: {media_id}")
                return media_id
            else:
                logger.error("Failed to upload reel")
                return None
                
        except PleaseWaitFewMinutes as e:
            logger.error(f"Instagram rate limit: {e}")
            # Record failed attempt
            self._record_failed_post(video_path, caption, str(e))
            return None
        except Exception as e:
            logger.error(f"Error posting reel: {e}")
            self._record_failed_post(video_path, caption, str(e))
            return None
    
    def _record_failed_post(self, video_path: Path, caption: str, error: str):
        """Record a failed post attempt"""
        try:
            failed_record = {
                "video_path": str(video_path),
                "caption": caption,
                "error": error,
                "attempt_date": datetime.now().isoformat()
            }
            
            self.post_history["failed_posts"].append(failed_record)
            self._save_post_history()
            
        except Exception as e:
            logger.error(f"Error recording failed post: {e}")
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get Instagram account information"""
        try:
            if not self.is_logged_in:
                if not self.login():
                    return None
            
            user_info = self.client.account_info()
            return {
                "username": user_info.username,
                "full_name": user_info.full_name,
                "follower_count": user_info.follower_count,
                "following_count": user_info.following_count,
                "media_count": user_info.media_count,
                "is_verified": user_info.is_verified,
                "is_business": user_info.is_business
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    def get_recent_posts(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent posts from the account"""
        try:
            if not self.is_logged_in:
                if not self.login():
                    return []
            
            user_id = self.client.user_id
            medias = self.client.user_medias(user_id, count)
            
            posts = []
            for media in medias:
                posts.append({
                    "id": media.id,
                    "caption": media.caption_text,
                    "like_count": media.like_count,
                    "comment_count": media.comment_count,
                    "taken_at": media.taken_at.isoformat(),
                    "media_type": media.media_type
                })
            
            return posts
            
        except Exception as e:
            logger.error(f"Error getting recent posts: {e}")
            return []
    
    def get_post_statistics(self) -> Dict[str, Any]:
        """Get posting statistics"""
        try:
            total_posts = self.post_history.get("total_posts", 0)
            failed_posts = len(self.post_history.get("failed_posts", []))
            success_rate = (total_posts / (total_posts + failed_posts)) * 100 if (total_posts + failed_posts) > 0 else 0
            
            last_post_date = self.post_history.get("last_post_date")
            if last_post_date:
                last_post = datetime.fromisoformat(last_post_date)
                hours_since_last_post = (datetime.now() - last_post).total_seconds() / 3600
            else:
                hours_since_last_post = None
            
            return {
                "total_successful_posts": total_posts,
                "total_failed_posts": failed_posts,
                "success_rate": success_rate,
                "last_post_date": last_post_date,
                "hours_since_last_post": hours_since_last_post,
                "can_post_now": self._can_post_now()
            }
            
        except Exception as e:
            logger.error(f"Error getting post statistics: {e}")
            return {}
    
    def logout(self):
        """Logout from Instagram"""
        try:
            if self.is_logged_in:
                self.client.logout()
                self.is_logged_in = False
                logger.info("Logged out from Instagram")
        except Exception as e:
            logger.error(f"Error logging out: {e}")
