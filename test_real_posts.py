#!/usr/bin/env python3
"""
Real Post Testing Script
Tests video generation improvements with actual posts and trending music
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.video_generators.text_video_generator import TextVideoGenerator
from src.video_generators.text_image_video_generator import TextImageVideoGenerator
from src.video_generators.video_text_generator import VideoTextGenerator
from src.audio_manager import AudioManager
from src.trending_music_manager import TrendingMusicManager
from src.message_processor import MessageProcessor

def setup_test_environment():
    """Setup test environment and ensure directories exist"""
    print("🔧 Setting up test environment...")
    
    # Create directories
    Config.create_directories()
    
    # Initialize trending music system
    print("🎵 Initializing trending music system...")
    trending_manager = TrendingMusicManager()
    audio_manager = AudioManager()
    
    # Force update trending music cache
    print("📥 Updating trending music cache...")
    success = trending_manager.update_trending_music(force_update=True)
    if success:
        print("✅ Trending music cache updated successfully")
    else:
        print("⚠️ Trending music update failed, will use fallback tracks")
    
    # Show available genres
    genres = audio_manager.get_trending_genres()
    print(f"🎼 Available music genres: {', '.join(genres)}")
    
    # Show music stats
    stats = audio_manager.get_trending_music_stats()
    print(f"📊 Music stats: {stats}")
    
    return audio_manager, trending_manager

def get_sample_posts():
    """Get sample posts for testing"""
    return [
        {
            "type": "text_only",
            "content": "🌟 امروز روز فوق‌العاده‌ای بود! کارهای زیادی انجام دادم و احساس موفقیت می‌کنم. گاهی اوقات باید از پیشرفت‌های کوچک خود قدردانی کنیم. 💪✨",
            "description": "Persian text with emojis - should use calming music"
        },
        {
            "type": "text_only", 
            "content": "This is a very long English message that will test our responsive text sizing and line breaking capabilities. It includes multiple emojis like 😊🎉⭐🔥💯 and should automatically adjust the font size to fit properly within the video frame without going out of bounds. The text should be positioned in the lower third of the video for that perfect Instagram-style look! 🎬📱",
            "description": "Long English text with many emojis - testing responsive sizing"
        },
        {
            "type": "text_image",
            "content": "🎨 Check out this amazing artwork! The colors and composition are absolutely stunning. Art has the power to inspire and transform our perspective. What do you think about this piece? 🖼️✨",
            "description": "Text with image - should use upbeat music and show image in upper area"
        },
        {
            "type": "text_only",
            "content": "💡 Quick tip: Success is not just about reaching your destination, but enjoying the journey along the way! 🚀",
            "description": "Short motivational text - testing minimal content"
        },
        {
            "type": "text_only",
            "content": "🎵 Music is the universal language that connects all hearts and souls across cultures and boundaries 🌍❤️🎶",
            "description": "Music-themed text - should get music-related trending audio"
        }
    ]

def create_test_image():
    """Create a test image for text+image videos"""
    from PIL import Image, ImageDraw, ImageFont
    
    # Create a colorful test image
    img = Image.new('RGB', (800, 600), color='#FF6B6B')
    draw = ImageDraw.Draw(img)
    
    # Add gradient effect
    for y in range(600):
        factor = y / 600
        r = int(255 * (1 - factor * 0.3))
        g = int(107 * (1 + factor * 0.5))
        b = int(107 * (1 + factor * 0.5))
        draw.line([(0, y), (800, y)], fill=(r, g, b))
    
    # Add some geometric shapes
    draw.ellipse([100, 100, 300, 300], fill='#4ECDC4', outline='white', width=5)
    draw.rectangle([400, 200, 700, 500], fill='#45B7D1', outline='white', width=5)
    
    # Add text
    try:
        font = ImageFont.truetype("arial.ttf", 48)
    except:
        font = ImageFont.load_default()
    
    draw.text((250, 350), "Test Artwork", font=font, fill='white', anchor='mm')
    draw.text((250, 400), "🎨✨", font=font, fill='white', anchor='mm')
    
    # Save test image
    test_image_path = Config.TEMP_DIR / "test_artwork.jpg"
    img.save(test_image_path, quality=95)
    
    return test_image_path

def test_text_only_videos(posts, audio_manager):
    """Test text-only video generation"""
    print("\n🎬 Testing Text-Only Videos with Trending Music...")
    
    text_gen = TextVideoGenerator()
    results = []
    
    text_posts = [p for p in posts if p["type"] == "text_only"]
    
    for i, post in enumerate(text_posts, 1):
        print(f"\n📝 Generating text-only video {i}/{len(text_posts)}")
        print(f"📄 Content preview: {post['content'][:50]}...")
        print(f"🎯 Test case: {post['description']}")
        
        # Generate filename with timestamp
        timestamp = int(time.time())
        filename = f"real_test_text_{i}_{timestamp}.mp4"
        output_path = Config.OUTPUT_DIR / filename
        
        # Test music selection
        print("🎵 Getting trending music...")
        music_track = audio_manager.get_random_track(
            genre_preference="lo-fi",
            avoid_recent=True
        )
        if music_track:
            print(f"🎼 Selected music: {music_track.name}")
        else:
            print("⚠️ No music track available")
        
        # Generate video
        start_time = time.time()
        try:
            success = text_gen.generate_video(post["content"], output_path)
            generation_time = time.time() - start_time
            
            if success:
                file_size = output_path.stat().st_size / (1024 * 1024)  # MB
                print(f"✅ Generated successfully: {filename}")
                print(f"⏱️ Generation time: {generation_time:.1f}s")
                print(f"📁 File size: {file_size:.1f}MB")
                
                results.append({
                    "filename": filename,
                    "success": True,
                    "generation_time": generation_time,
                    "file_size_mb": file_size,
                    "music_used": music_track.name if music_track else None
                })
            else:
                print(f"❌ Failed to generate: {filename}")
                results.append({"filename": filename, "success": False})
                
        except Exception as e:
            print(f"❌ Error generating {filename}: {e}")
            results.append({"filename": filename, "success": False, "error": str(e)})
    
    return results

def test_text_image_videos(posts, audio_manager):
    """Test text+image video generation"""
    print("\n🖼️ Testing Text+Image Videos with Trending Music...")
    
    text_image_gen = TextImageVideoGenerator()
    results = []
    
    # Create test image
    test_image = create_test_image()
    print(f"🎨 Created test image: {test_image}")
    
    text_image_posts = [p for p in posts if p["type"] == "text_image"]
    
    for i, post in enumerate(text_image_posts, 1):
        print(f"\n🖼️ Generating text+image video {i}/{len(text_image_posts)}")
        print(f"📄 Content preview: {post['content'][:50]}...")
        print(f"🎯 Test case: {post['description']}")
        
        # Generate filename with timestamp
        timestamp = int(time.time())
        filename = f"real_test_text_image_{i}_{timestamp}.mp4"
        output_path = Config.OUTPUT_DIR / filename
        
        # Test music selection for upbeat content
        print("🎵 Getting upbeat trending music...")
        music_track = audio_manager.get_random_track(
            genre_preference="upbeat",
            avoid_recent=True
        )
        if music_track:
            print(f"🎼 Selected music: {music_track.name}")
        else:
            print("⚠️ No music track available")
        
        # Generate video
        start_time = time.time()
        try:
            success = text_image_gen.generate_video(
                post["content"],
                [test_image],
                output_path
            )
            generation_time = time.time() - start_time
            
            if success:
                file_size = output_path.stat().st_size / (1024 * 1024)  # MB
                print(f"✅ Generated successfully: {filename}")
                print(f"⏱️ Generation time: {generation_time:.1f}s")
                print(f"📁 File size: {file_size:.1f}MB")
                
                results.append({
                    "filename": filename,
                    "success": True,
                    "generation_time": generation_time,
                    "file_size_mb": file_size,
                    "music_used": music_track.name if music_track else None
                })
            else:
                print(f"❌ Failed to generate: {filename}")
                results.append({"filename": filename, "success": False})
                
        except Exception as e:
            print(f"❌ Error generating {filename}: {e}")
            results.append({"filename": filename, "success": False, "error": str(e)})
    
    return results

def generate_test_report(text_results, image_results, audio_manager):
    """Generate comprehensive test report"""
    print("\n📊 Generating Test Report...")
    
    all_results = text_results + image_results
    successful = [r for r in all_results if r.get("success")]
    failed = [r for r in all_results if not r.get("success")]
    
    # Calculate stats
    total_videos = len(all_results)
    success_rate = len(successful) / total_videos * 100 if total_videos > 0 else 0
    avg_generation_time = sum(r.get("generation_time", 0) for r in successful) / len(successful) if successful else 0
    avg_file_size = sum(r.get("file_size_mb", 0) for r in successful) / len(successful) if successful else 0
    
    # Music usage stats
    music_stats = audio_manager.get_trending_music_stats()
    
    # Create report
    report = {
        "test_timestamp": datetime.now().isoformat(),
        "summary": {
            "total_videos": total_videos,
            "successful": len(successful),
            "failed": len(failed),
            "success_rate": f"{success_rate:.1f}%",
            "avg_generation_time": f"{avg_generation_time:.1f}s",
            "avg_file_size": f"{avg_file_size:.1f}MB"
        },
        "music_system": music_stats,
        "successful_videos": successful,
        "failed_videos": failed
    }
    
    # Save report
    report_path = Config.OUTPUT_DIR / f"test_report_{int(time.time())}.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n🎯 TEST RESULTS SUMMARY")
    print(f"{'='*50}")
    print(f"📊 Total Videos: {total_videos}")
    print(f"✅ Successful: {len(successful)} ({success_rate:.1f}%)")
    print(f"❌ Failed: {len(failed)}")
    print(f"⏱️ Avg Generation Time: {avg_generation_time:.1f}s")
    print(f"📁 Avg File Size: {avg_file_size:.1f}MB")
    print(f"🎵 Music System Stats: {music_stats}")
    print(f"📄 Detailed report saved: {report_path}")
    
    if successful:
        print(f"\n✅ SUCCESSFUL VIDEOS:")
        for result in successful:
            music_info = f" (Music: {result.get('music_used', 'None')})" if result.get('music_used') else ""
            print(f"  📹 {result['filename']}{music_info}")
    
    if failed:
        print(f"\n❌ FAILED VIDEOS:")
        for result in failed:
            error_info = f" - {result.get('error', 'Unknown error')}" if result.get('error') else ""
            print(f"  ❌ {result['filename']}{error_info}")
    
    return report

def main():
    """Run comprehensive real post testing"""
    print("🚀 Starting Real Post Video Generation Test")
    print("=" * 60)
    
    # Setup
    audio_manager, trending_manager = setup_test_environment()
    
    # Get test posts
    posts = get_sample_posts()
    print(f"\n📝 Testing with {len(posts)} sample posts")
    
    # Run tests
    text_results = test_text_only_videos(posts, audio_manager)
    image_results = test_text_image_videos(posts, audio_manager)
    
    # Generate report
    report = generate_test_report(text_results, image_results, audio_manager)
    
    print(f"\n🎬 All videos saved to: {Config.OUTPUT_DIR}")
    print("🔍 Please review the generated videos and provide feedback!")
    print("\n💡 What to check:")
    print("  • Text positioning in lower third")
    print("  • Responsive text sizing and line breaks")
    print("  • Image positioning in upper area (for text+image)")
    print("  • Music quality and genre appropriateness")
    print("  • Overall Instagram-style appearance")

if __name__ == "__main__":
    main()
